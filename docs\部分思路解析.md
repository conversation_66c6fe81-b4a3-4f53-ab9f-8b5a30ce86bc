### 硬件信息获取
硬件信息获取Python脚本示例
```python
import json
import traceback
import wmi
import datetime


def wmi_date_to_str(wmi_date):
    """Converts a WMI CIM_DATETIME to an ISO 8601 string."""
    if wmi_date is None:
        return None
    try:
        # Format is yyyymmddhhmmss.ffffff+|-zzz
        dt = datetime.datetime.strptime(wmi_date.split(".")[0], "%Y%m%d%H%M%S")
        return dt.isoformat()
    except (ValueError, IndexError):
        return str(wmi_date)


def get_system_info():
    """获取操作系统和基本系统信息"""
    try:
        c = wmi.WMI()
        os_info = c.Win32_OperatingSystem()[0]

        return {
            "caption": os_info.Caption,
            "version": os_info.Version,
            "os_architecture": os_info.OSArchitecture,
            "hostname": os_info.CSName,
        }
    except Exception as e:
        return {"error": str(e), "traceback": traceback.format_exc()}


def get_cpu_info():
    """获取CPU详细信息"""
    try:
        c = wmi.WMI()

        processors = []
        for p in c.Win32_Processor():
            processors.append(
                {
                    "device_id": p.DeviceID,
                    "name": p.Name,
                    "manufacturer": p.Manufacturer,
                    "current_clock_speed": f"{p.CurrentClockSpeed} MHz",
                    "max_clock_speed": f"{p.MaxClockSpeed} MHz",
                }
            )

        return {
            "wmi_processor_info": processors,
        }
    except Exception as e:
        return {"error": str(e), "traceback": traceback.format_exc()}


def get_memory_info():
    """获取内存信息"""
    try:
        c = wmi.WMI()
        physical_memory = []

        for mem in c.Win32_PhysicalMemory():
            physical_memory.append(
                {
                    "capacity": f"{int(mem.Capacity) / (1024**3):.2f} GB",
                    "part_number": mem.PartNumber.strip(),
                    "speed": f"{mem.Speed} MHz",
                }
            )

        return {
            "physical_slots": physical_memory,
        }
    except Exception as e:
        return {"error": str(e), "traceback": traceback.format_exc()}


def get_disk_info():
    """获取磁盘信息"""
    try:
        c = wmi.WMI()
        physical_disks = []

        for disk in c.Win32_DiskDrive():
            physical_disks.append(
                {
                    "model": disk.Model,
                    "size": f"{int(disk.Size) / (1024**3):.2f} GB",
                }
            )

        return {
            "physical_disks": physical_disks,
        }
    except Exception as e:
        return {"error": str(e), "traceback": traceback.format_exc()}


def get_gpu_info():
    """获取GPU信息"""
    try:
        c = wmi.WMI()
        gpus = []

        for gpu in c.Win32_VideoController():
            pnp_id = gpu.PNPDeviceID
            if pnp_id and "PCI" in pnp_id:
                gpus.append(
                    {
                        "name": gpu.Name,
                        "pnp_device_id": pnp_id,
                        "driver_version": gpu.DriverVersion,
                        "current_refresh_rate": gpu.CurrentRefreshRate,
                        "current_horizontal_resolution": gpu.CurrentHorizontalResolution,
                        "current_vertical_resolution": gpu.CurrentVerticalResolution,
                    }
                )
        return {"gpus": gpus}
    except Exception as e:
        return {"error": str(e), "traceback": traceback.format_exc()}


def get_motherboard_info():
    """获取主板和BIOS信息"""
    try:
        c = wmi.WMI()
        baseboard = c.Win32_BaseBoard()[0]
        bios = c.Win32_BIOS()[0]
        return {
            "motherboard": {
                "manufacturer": baseboard.Manufacturer,
                "product": baseboard.Product,
            },
            "bios": {
                "manufacturer": bios.Manufacturer,
                "version": bios.SMBIOSBIOSVersion,
                "release_date": wmi_date_to_str(bios.ReleaseDate),
            },
        }
    except Exception as e:
        return {"error": str(e), "traceback": traceback.format_exc()}


def main():
    """主函数，聚合所有信息并写入JSON文件"""
    hardware_info = {
        "system": get_system_info(),
        "cpu": get_cpu_info(),
        "memory": get_memory_info(),
        "disk": get_disk_info(),
        "gpu": get_gpu_info(),
        "motherboard": get_motherboard_info(),
    }

    with open("hardware_info.json", "w", encoding="utf-8") as f:
        json.dump(hardware_info, f, indent=4, ensure_ascii=False)

    print("硬件信息已成功写入 hardware_info.json 文件。")


if __name__ == "__main__":
    main()

```

获取到的JSON文件内容如下：
```json
{
    "system": {
        "caption": "Microsoft Windows 11 专业工作站版 Insider Preview",
        "version": "10.0.26200",
        "os_architecture": "64 位",
        "hostname": "DESKTOP-587MD10"
    },
    "cpu": {
        "wmi_processor_info": [
            {
                "device_id": "CPU0",
                "name": "Intel(R) Core(TM) i7-14700KF",
                "manufacturer": "GenuineIntel",
                "current_clock_speed": "5700 MHz",
                "max_clock_speed": "5701 MHz"
            }
        ]
    },
    "memory": {
        "physical_slots": [
            {
                "capacity": "16.00 GB",
                "part_number": "M-POWER8036AW16",
                "speed": "8000 MHz"
            },
            {
                "capacity": "16.00 GB",
                "part_number": "M-POWER8036AW16",
                "speed": "8000 MHz"
            }
        ]
    },
    "disk": {
        "physical_disks": [
            {
                "model": "PHIXERO",
                "size": "1907.73 GB"
            },
            {
                "model": "XPG GAMMIX S70 BLADE",
                "size": "953.86 GB"
            }
        ]
    },
    "gpu": {
        "gpus": [
            {
                "name": "NVIDIA GeForce RTX 4070 SUPER",
                "pnp_device_id": "PCI\\VEN_10DE&DEV_2783&SUBSYS_15007377&REV_A1\\4&4B57A0D&0&0008",
                "driver_version": "32.0.15.6094",
                "current_refresh_rate": 160,
                "current_horizontal_resolution": 3840,
                "current_vertical_resolution": 2160
            }
        ]
    },
    "motherboard": {
        "motherboard": {
            "manufacturer": "Micro-Star International Co., Ltd.",
            "product": "Z790MPOWER (MS-7E01)"
        },
        "bios": {
            "manufacturer": "American Megatrends International, LLC.",
            "version": "P.21U4",
            "release_date": "2024-04-10T00:00:00"
        }
    }
}
``` 



### 超频工具图标获取
超频工具图标获取Python脚本示例：
```python
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图标提取工具
提供从可执行文件中提取图标的功能

此模块主要用于从Windows可执行文件(.exe)中提取不同尺寸的图标，
支持获取最佳尺寸的图标和图标的尺寸信息，为应用程序提供视觉展示。
"""

import os
from typing import List, Optional, Dict, Union
from PySide6.QtCore import QFileInfo, QSize
from PySide6.QtGui import QIcon, QPixmap, QImage
from PySide6.QtWidgets import QFileIconProvider


class IconExtractor:
    """图标提取器

    提供从文件中提取图标的功能，特别是Windows可执行文件(.exe)
    支持获取最佳尺寸的图标和图标的尺寸信息
    """

    def __init__(self):
        """初始化图标提取器

        创建文件图标提供器实例
        """
        self._icon_provider = QFileIconProvider()

    def get_file_icon(self, file_path: str) -> QIcon:
        """获取文件图标

        Args:
            file_path: 文件路径

        Returns:
            QIcon: 文件的图标对象

        Note:
            如果文件不存在或路径无效，将返回空图标
        """
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return QIcon()

        # 创建QFileInfo对象
        file_info = QFileInfo(file_path)

        # 通过QFileIconProvider获取图标
        return self._icon_provider.icon(file_info)

    def get_icon_sizes(self, icon: QIcon) -> List[QSize]:
        """获取图标的可用尺寸列表

        Args:
            icon: 要检查的图标对象

        Returns:
            List[QSize]: 图标可用尺寸的列表
        """
        if icon.isNull():
            return []

        return icon.availableSizes()

    def get_best_icon_pixmap(
        self, icon: QIcon, requested_size: QSize = QSize(64, 64)
    ) -> QPixmap:
        """获取最佳图标像素图

        Args:
            icon: 图标对象
            requested_size: 请求的图标尺寸，默认为64x64

        Returns:
            QPixmap: 最佳图标像素图

        Algorithm:
            1. 检查图标是否为空，如果为空返回空QPixmap
            2. 获取图标的所有可用尺寸
            3. 如果有可用尺寸，选择宽度不大于请求尺寸且最接近请求尺寸的尺寸
            4. 如果没有合适的尺寸，则使用请求的尺寸
            5. 返回所选尺寸的图标像素图
        """
        if icon.isNull():
            return QPixmap()

        # 获取图标的可用尺寸列表
        sizes = icon.availableSizes()

        if sizes:
            # 找到接近请求尺寸的最大尺寸
            best_size = max(
                sizes,
                key=lambda s: s.width() if s.width() <= requested_size.width() else 0,
            )

            # 如果找到了合适的尺寸，使用它；否则使用请求的尺寸
            use_size = best_size if best_size.width() > 0 else requested_size
            return icon.pixmap(use_size)

        # 如果图标没有可用尺寸，使用请求的尺寸
        return icon.pixmap(requested_size)

    def get_icon_info(self, file_path: str) -> Dict[str, Union[bool, str, List[str]]]:
        """获取文件图标的详细信息

        Args:
            file_path: 文件路径

        Returns:
            Dict: 包含图标是否有效、图标尺寸信息的字典

        Example:
            返回格式示例:
            {
                'valid': True,
                'path': 'C:/path/to/file.exe',
                'sizes': ['16x16', '32x32', '48x48', '64x64']
            }
        """
        icon = self.get_file_icon(file_path)
        sizes = self.get_icon_sizes(icon)

        return {
            "valid": not icon.isNull(),
            "path": file_path,
            "sizes": [f"{size.width()}x{size.height()}" for size in sizes],
        }


# 单例模式
_icon_extractor_instance = None


def get_icon_extractor() -> IconExtractor:
    """获取IconExtractor实例(单例模式)

    确保整个应用程序中只有一个IconExtractor实例

    Returns:
        IconExtractor: 图标提取器实例
    """
    global _icon_extractor_instance
    if _icon_extractor_instance is None:
        _icon_extractor_instance = IconExtractor()
    return _icon_extractor_instance
```