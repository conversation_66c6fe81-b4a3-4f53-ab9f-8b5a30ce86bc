### **项目需求：Windows硬件工具箱开发（PySide6 + PySide6-Fluent-Widgets）**

#### **1. 界面要求**
- **布局**：
  - 左侧导航栏，右侧内容区域
  - 支持全局主题切换（浅色/深色/跟随系统模式）
- **现代化设计**：
  - **必须要求**：在PySide6-Fluent-Widgets组件库如果有对应的组件/组件实现方案，那么必须使用对应的组件，或者类似的组件实现
  - 遵循Fluent Design2.0设计规范（参考文档：[Microsoft Fluent Design](https://learn.microsoft.com/zh-cn/windows/apps/design/)）。
  - PySide6-Fluent-Widgets组件库文档：https://github.com/zhiyiYo/PyQt-Fluent-Widgets/tree/PySide6
  - 除快捷工具需要根据配置文件分类字段进行分类以外的其他视图无需分类、搜索、收藏、刷新等功能。必须严格按需求开发，绝对禁止添加额外功能
  - 整个项目所有视图界面都不使用折叠，平铺式布局
- **风格化图标**：
- **软件图标**：
  - 使用`assets\icon.ico`作为应用图标。
  - 在使用PySide6-Fluent-Widgets中FluentIcon提供的图标前，你必须执行`python -c "from qfluentwidgets import FluentIcon; print([i for i in dir(FluentIcon) if not i.startswith('_')])"`Python脚本来获取所有可用的FluentIcon图标，避免使用不存在的FluentIcon图标
  - 除导航栏以外需要使用图标，视图界面禁止使用任何图标
- **启动动画**：
  - 包含图标、软件名称、版本、版权信息。
  - 必须以管理员方式运行，若未以管理员模式启动，弹出对话框提醒用户。并在所有涉及系统级操作前再次检测权限。
- **架构设计要求**
  采用松耦合的 MVP（Model-View-Presenter）架构


#### **2. 功能模块要求**
- 软件启动动画
  -使用PySide6-Fluent-Widgets组件库的splashscreen组件编写启动动画
- **硬件信息**：
  - 获取电脑详细硬件配置信息（可参考`部分思路解析.md`中的硬件信息获取方案，但不是完全复制）。
  - 界面中仅显示该文档中列出的JSON数据。
  - **必须注意**：异步获取硬件信息，在启动动画期间预加载
  - **界面要求**：
    - 硬件信息直观简洁，不允许使用折叠和滚动，平铺展示，需要复制硬件信息的按钮功能
    - 界面、导出、复制应该显示硬件信息的中文名字，而不是获取到的硬件信息返回的json数据键的名字

- **优化清理**：
  - **必须注意**：异步执行所有优化清理操作，显示执行进度和状态反馈
  - **全局控制要求**：
    - 实现界面级全选复选框
    - 全选功能：一键选择/取消所有选项卡中的所有选项
    - 执行功能：支持一键执行所有任务或仅执行选中任务
  - **执行后操作**：重启文件资源管理器 + 删除 `iconcache.db` 文件
  - **选项卡分类**：
    1. **PowerShell设置**：必须使用复选框，以便实现全局控制要求。
       - 设置执行策略为`Bypass`（配置文件：`powershell_commands.py`中的`EXECUTION_POLICY_COMMAND`）。
       - 解锁电源高级选项（配置文件：`powershell_commands.py`中的`UNLOCK_POWER_COMMAND`）。
    2. **注册表优化**（配置文件：`registry_commands.py`）：必须使用复选框，以便实现全局控制要求。
       - 父子复选框联动规则：如父选中则所有子选中，子全选父自动选中，部分子选中父为半选。
       - 例如：任务栏相关设置为父，任务栏精确到秒为子。
       - 若任务包含多条注册表命令，只要有一条成功即视为执行成功。
    3. **系统清理**（配置文件：`system_cleanup.py`）：必须使用复选框，以便实现全局控制要求。
       - 清理系统临时文件、临时目录、预读取文件、用户临时文件、IE缓存、Web缓存、系统日志、回收站、更新缓存、缩略图缓存、DNS缓存。
       - 运行系统磁盘清理。

- **预装应用**：
  - **必须注意**：需要异步执行应用卸载操作，显示卸载进度和错误处理
  - **全局控制要求**：
    - 实现界面级全选复选框
    - 支持一键选择/取消所有选项卡中的所有选项
    - 支持一键执行所有任务或仅执行选中任务
  - **选项卡分类**（配置文件：`appx_packages.py`，在执行卸载时使用通配符的方式在系统中查找对应的应用包名，然后使用查找的完整包名进行卸载预装应用包和卸载用户安装的应用包
）：必须使用复选框，以便实现全局控制要求。
    1. Windows Xbox相关应用。
    2. Windows Store商店应用。
    3. Windows音视频编解码器应用。
    4. Windows系统预装应用。
    5. 卸载并清理OneDrive（配置文件：`onedrive_cleanup.py`）：必须使用复选框，以便实现全局控制要求。
      - 停止OneDrive进程。
      - 卸载OneDrive UWP/传统应用。
      - 清理OneDrive文件夹、注册表项。
      - 禁用OneDrive文件资源管理器集成。
      - 清理OneDrive启动项。

- **超频工具**：
  - **必须注意**：新建子进程打开超频工具
  - 提供一键进入BIOS按钮（需用户二次确认）。
  - 扫描`OCTools`文件夹中的可执行程序，支持用户自行添加工具。
    - 检查`OCTools`文件夹是否存在，不存在则创建。
    - 以文件夹名称作为工具名称，可执行路径为实际工具路径。
    - 提取可执行文件图标（可参考`部分思路解析.md`中的图标获取方案，但不是完全复制，只参考实际需要用到的部分）。
    - 启动前检查可执行文件类型（GUI/控制台程序），以适合方式运行。
    - 优先使用与文件夹同名的可执行文件，否则查找其他可执行文件。
    - 所有工具均以管理员方式运行。

- **快捷工具**（配置文件：`quick_tools.py`）：
  - **必须注意**：使用新建子进程打开快捷工具
  - 根据提供的配置文件分类。
  - 以下工具需用户二次确认：
    - 安全模式（一次性操作，重启后恢复为正常模式）。
    - 重新启动、关机、睡眠、锁定计算机。
  - 其他工具点击按钮小卡片即可启动。
  

- **设置关于**：
  - **居中显示**：应用图标（`assets/icon.ico`）、名称、版本号、版权信息。
  - **主题配置**：深/浅/跟随系统。设置全局主题。界面无需设置颜色。
  - **检查更新**：直接跳转官网
  - **赞助作者**：点击弹出自定义的对话框（内置选项卡组件），显示支付宝（`alipay.png`）和微信（`wechat.png`）二维码。
  - **抖音主页**：点击跳转作者抖音主页。
  - **官方Q群**：点击跳转QQ群。

#### **3. 配置文件要求**
    - 配置文件必须只引用提供的配置文件，不允许修改配置文件内容。


#### **4. 项目结构要求**
```
├── main.py                      # 程序入口点
├── app.py                       # 应用程序主类
├── splash.py                    # 启动动画实现
├── admin_check.py               # 管理员权限验证
├── assets/                      # 静态资源文件
│   ├── icon.ico                # 应用主图标
│   ├── alipay.png              # 支付宝收款码
│   └── wechat.png          # 微信收款码
├── config/                      # 配置文件模块
│   ├── powershell_commands.py  # PowerShell 命令配置
│   ├── registry_commands.py    # 注册表操作配置
│   ├── system_cleanup.py       # 系统清理任务配置
│   ├── appx_packages.py        # UWP 应用包配置
│   ├── onedrive_cleanup.py     # OneDrive 清理配置
│   └── quick_tools.py          # 快捷工具配置
├── core/                        # Model 层 - 核心业务逻辑
│   ├── __init__.py
│   ├── hardware_info.py        # 硬件信息获取服务
│   ├── system_optimizer.py     # 系统优化服务
│   ├── app_manager.py          # 应用管理服务
│   ├── oc_tools_scanner.py     # 超频工具扫描服务
│   ├── quick_tools_manager.py  # 快捷工具管理服务
│   └── updater.py              # 软件更新检查服务
├── ui/                          # View 层 - 用户界面
│   ├── __init__.py
│   ├── main_window.py          # 主窗口容器
│   ├── navigation.py           # 左侧导航栏
│   └── views/                  # 功能视图模块
│       ├── __init__.py
│       ├── hardware_view.py    # 硬件信息展示视图
│       ├── optimization_view.py # 系统优化清理视图
│       ├── preinstalled_view.py # 预装应用管理视图
│       ├── overclocking_view.py # 超频工具管理视图
│       ├── quick_tools_view.py # 快捷工具视图
│       └── settings_view.py    # 设置与关于视图
├── presenters/                  # Presenter 层 - 业务协调
│   ├── __init__.py
│   ├── hardware_presenter.py   # 硬件信息业务协调
│   ├── optimization_presenter.py # 系统优化业务协调
│   ├── preinstalled_presenter.py # 预装应用业务协调
│   ├── overclocking_presenter.py # 超频工具业务协调
│   ├── quick_tools_presenter.py # 快捷工具业务协调
│   └── settings_presenter.py   # 设置关于业务协调
├── utils/                       # 通用工具类
│   ├── __init__.py
│   ├── system_utils.py         # 系统操作工具
│   ├── icon_extractor.py       # 可执行文件图标提取
│   ├── process_utils.py        # 进程管理工具
│   └── dialog_utils.py         # 对话框封装工具
└── OCTools/                     # 超频工具存放目录（打包时排除）
```